"use client";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createGetRequest } from "@/shared/lib/requests";
import { IResponsePaginated } from "@/shared/types/requests/response-paginated.type";
import { useQuery } from "@tanstack/react-query";
import { IPaginationParameters } from "../../../../../shared/types/pagination/types";
import { inspectionKeys } from "../../../constants/query/keys";
import { IFormDto } from "../../../types/forms/dtos/find-all.dto";

export const useFindAllForms = ({ page = 1, limit = 10, search = "" }: IPaginationParameters = {}) => {
	const { data, isLoading, isFetched } = useQuery({
		queryKey: inspectionKeys.forms.list({ page, limit, search }),
		queryFn: () => createGetRequest<IResponsePaginated<IFormDto>>(INSPECTION_FORM_ENDPOINTS.FIND_ALL({ page, limit, search })),
	});

	const isNoDataFound = !data?.success && data?.status === 404;
	return {
		data: data?.success ? data.data.data : [],
		pagination: data?.success
			? {
					totalItems: data.data.totalItems,
					itemsPerPage: data.data.itemsPerPage,
					currentPage: data.data.currentPage,
					totalPages: data.data.totalPages,
				}
			: null,
		isLoading,
		hasError: isFetched && !data?.success && !isNoDataFound,
		error: !data?.success && !isNoDataFound && data?.data.message,
		isEmpty: isNoDataFound,
	};
};
