import { ACTION_LIST, IDefineCaslPermission, ROLE_LIST, SUBJECT_LIST, TPermissionAction, TPermissionSubject, TRoleList } from "@/config/permissions";
import { IUser } from "@/core/auth/types/user.types";
import { decodeJWT } from "@/shared/lib/jwt/decode";
import { IDecodedAccessToken } from "@/shared/types/cookies/decoded-access-token";
import { NextRequest } from "next/server";

export interface ITokenValidationResult {
	isValid: boolean;
	user?: IUser;
	error?: string;
}

export interface ITokenValidationService {
	validateToken(request: NextRequest): Promise<ITokenValidationResult>;
	extractUserFromToken(decodedToken: IDecodedAccessToken): IUser;
}

export class TokenValidationService implements ITokenValidationService {
	private static instance: TokenValidationService;
	private tokenCache = new Map<string, { user: IUser; expiresAt: number }>();
	private readonly CACHE_TTL = 5 * 60 * 1000;

	public static getInstance(): TokenValidationService {
		if (!TokenValidationService.instance) TokenValidationService.instance = new TokenValidationService();
		return TokenValidationService.instance;
	}

	public async validateToken(request: NextRequest): Promise<ITokenValidationResult> {
		try {
			const { pathname } = request.nextUrl;
			if (this.isPublicRoute(pathname)) return { isValid: true };

			const accessToken = request.cookies.get("access_token")?.value;
			// const refreshToken = request.cookies.get("refresh_token")?.value;

			if (!accessToken)
				return {
					isValid: false,
					error: "Token de acesso não encontrado",
				};

			const token = accessToken;
			const cacheKey = `${token}_${pathname}`;

			const cached = this.tokenCache.get(cacheKey);
			if (cached && Date.now() < cached.expiresAt) return { isValid: true, user: cached.user };

			const decodedToken = decodeJWT<IDecodedAccessToken>(token);
			if (!decodedToken) {
				return {
					isValid: false,
					error: "Token inválido ou expirado",
				};
			}

			if (this.isTokenExpired(decodedToken)) {
				return {
					isValid: false,
					error: "Token expirado",
				};
			}

			const user = this.extractUserFromToken(decodedToken);
			this.tokenCache.set(cacheKey, {
				user,
				expiresAt: Date.now() + this.CACHE_TTL,
			});

			return { isValid: true, user };
		} catch (error) {
			console.error("Erro na validação do token:", error);
			return {
				isValid: false,
				error: "Erro interno na validação",
			};
		}
	}

	public extractUserFromToken(decodedToken: IDecodedAccessToken): IUser {
		return {
			id: decodedToken.sub ? String(decodedToken.sub) : "",
			name: decodedToken.name ? String(decodedToken.name) : "",
			email: decodedToken.email ? String(decodedToken.email) : "",
			roles: this.extractRoles(decodedToken),
			permissions: this.extractPermissions(decodedToken),
		};
	}

	private isPublicRoute(pathname: string): boolean {
		return pathname.startsWith("/auth/") || pathname.startsWith("/api/") || pathname.includes(".") || pathname === "/favicon.ico";
	}

	private isTokenExpired(decodedToken: IDecodedAccessToken): boolean {
		const currentTime = Math.floor(Date.now() / 1000);
		return decodedToken.exp < currentTime;
	}

	private extractRoles(decodedToken: IDecodedAccessToken): TRoleList[] {
		if (
			typeof decodedToken.resource_access === "object" &&
			decodedToken.resource_access !== null &&
			"simp" in decodedToken.resource_access &&
			decodedToken.resource_access.simp &&
			typeof decodedToken.resource_access.simp === "object" &&
			Array.isArray((decodedToken.resource_access.simp as { roles?: unknown }).roles)
		) {
			return ((decodedToken.resource_access.simp as { roles: string[] }).roles || [])
				.filter((r): r is TRoleList => Object.values(ROLE_LIST).includes(r as TRoleList))
				.map(r => r as TRoleList);
		}
		return [];
	}

	private extractPermissions(decodedToken: IDecodedAccessToken): IDefineCaslPermission[] {
		type PermissionPayload = { scopes?: string[]; rsname?: string };
		const validActions: TPermissionAction[] = Object.values(ACTION_LIST);
		const validSubjects: TPermissionSubject[] = Object.values(SUBJECT_LIST);

		if (
			typeof decodedToken.authorization === "object" &&
			decodedToken.authorization !== null &&
			Array.isArray((decodedToken.authorization as { permissions?: unknown }).permissions)
		) {
			return (decodedToken.authorization as { permissions: PermissionPayload[] }).permissions.flatMap(p => {
				const actions = p.scopes && p.scopes.length > 0 ? p.scopes : ["manage"];
				return actions
					.filter((action): action is TPermissionAction => validActions.includes(action as TPermissionAction))
					.map(action => ({
						action: action as TPermissionAction,
						subject: p.rsname && validSubjects.includes(p.rsname as TPermissionSubject) ? (p.rsname as TPermissionSubject) : "all",
					}));
			});
		}
		return [];
	}

	public clearCache(): void {
		this.tokenCache.clear();
	}

	public getCacheSize(): number {
		return this.tokenCache.size;
	}
}
