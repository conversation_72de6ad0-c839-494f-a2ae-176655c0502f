"use client";
import { toast } from "@/core/toast";
import { createDeleteRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { PRODUCT_TYPE_ENDPOINTS } from "../../api/endpoints";
import { productTypeKeys } from "../../constants/query";

export const useDeleteProductTypeMutation = () => {
	const queryClient = useQueryClient();

	const deleteProductType = useMutation({
		mutationKey: productTypeKeys.custom("delete"),
		mutationFn: async (id: string) => {
			const { data, success } = await createDeleteRequest<IMessageGlobalReturn>(PRODUCT_TYPE_ENDPOINTS.DELETE(id));
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => productTypeKeys.invalidateAll(queryClient),
	});

	return {
		deleteProductType: (id: string) =>
			toast.promise(deleteProductType.mutateAsync(id), {
				loading: "Deletando tipo de produto...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
