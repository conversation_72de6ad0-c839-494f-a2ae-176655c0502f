import { ProtectedComponent } from "@/shared/components/auth/protected";
import { <PERSON><PERSON> } from "@/shared/components/shadcn/button";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { Edit, Trash } from "lucide-react";
import { DeleteKnowledgeModal } from "../delete/delete-knowledge-modal";
import { EditKnowledgeModal } from "../edit/edit-knowledge-modal";

export const ChatKnowledgeActions = ({ knowledgeId, title }: { knowledgeId: string; title: string }) => {
	const modals = {
		edit: useModal(),
		delete: useModal(),
	};

	return (
		<div className="pr-[10px] text-right">
			<ProtectedComponent action="update" subject="all">
				<Button size="icon" onClick={modals.edit.openModal} className="bg-primary/5 hover:bg-primary/40 group h-8 w-8">
					<Edit className="text-primary h-4 w-4 group-hover:text-white" />
				</Button>
			</ProtectedComponent>
			<ProtectedComponent action="delete" subject="all">
				<Button size="icon" onClick={modals.delete.openModal} className="group ml-2 h-8 w-8 bg-red-500/5 hover:bg-red-500/30">
					<Trash className="h-4 w-4 text-red-500 group-hover:text-white" />
				</Button>
			</ProtectedComponent>
			<EditKnowledgeModal isOpen={modals.edit.isOpen} onClose={modals.edit.closeModal} id={knowledgeId} />
			<DeleteKnowledgeModal isOpen={modals.delete.isOpen} onClose={modals.delete.closeModal} title={title} id={knowledgeId} />
		</div>
	);
};
