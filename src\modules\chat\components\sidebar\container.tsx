"use client";
import { AnimatePresence, motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { ArrowDown, MessageSquare } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { chatIsOpenAtom } from "../../atoms/controls/trigger.atom";
import { chatStreamingMessagesAtom, isMessagesAvailableAtom } from "../../atoms/session/info.atom";
import { useSessionManager } from "../../hooks/streaming/session-manager.hook";
import { useStreamingManager } from "../../hooks/streaming/streaming-manager.hook";
import { ChatMessage } from "./content";
import { ChatHeader } from "./header";
import { ChatInput } from "./input";

export const ChatSidebar = () => {
	const isOpen = useAtomValue(chatIsOpenAtom);
	useSessionManager({ isOpen });
	const { sendMessage, stop, isStreaming } = useStreamingManager();
	const scrollAreaRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLTextAreaElement | null>(null);
	const hasMessages = useAtomValue(isMessagesAvailableAtom);
	const messages = useAtomValue(chatStreamingMessagesAtom);

	// Controle de auto-scroll e UI
	const [isAtBottom, setIsAtBottom] = useState(true);
	const [autoScroll, setAutoScroll] = useState(true);
	const [hasNewMessages, setHasNewMessages] = useState(false);

	const computeIsAtBottom = useCallback((el: HTMLDivElement | null) => {
		if (!el) return true;
		const threshold = 24; // px de tolerância
		const distance = el.scrollHeight - el.scrollTop - el.clientHeight;
		return distance <= threshold;
	}, []);

	const scrollToBottom = useCallback((behavior: ScrollBehavior = "auto") => {
		const el = scrollAreaRef.current;
		if (!el) return;
		el.scrollTo({ top: el.scrollHeight, behavior });
	}, []);

	// Auto-focus ao abrir

	useEffect(() => {
		if (isOpen) {
			setTimeout(() => {
				inputRef.current?.focus();
			}, 100);
		}
	}, [isOpen]);

	// Ajusta estados ao abrir o chat
	useEffect(() => {
		if (!isOpen) return;
		// Garante que comece no fim
		requestAnimationFrame(() => {
			scrollToBottom("auto");
			const el = scrollAreaRef.current;
			const atBottom = computeIsAtBottom(el);
			setIsAtBottom(atBottom);
			setAutoScroll(true);
			setHasNewMessages(false);
		});
	}, [isOpen, computeIsAtBottom, scrollToBottom]);

	// Reage a novas mensagens
	useEffect(() => {
		const el = scrollAreaRef.current;
		if (!el) return;

		if (autoScroll) {
			// Mantém no fim quando permitido
			scrollToBottom(isOpen ? "smooth" : "auto");
			setHasNewMessages(false);
			setIsAtBottom(true);
		} else {
			// Usuário está no histórico; marca novas mensagens
			setHasNewMessages(true);
			setIsAtBottom(computeIsAtBottom(el));
		}
	}, [messages, autoScroll, computeIsAtBottom, isOpen, scrollToBottom]);

	// Handler de scroll manual
	const handleScroll: React.UIEventHandler<HTMLDivElement> = useCallback(
		e => {
			const el = e.currentTarget;
			const atBottom = computeIsAtBottom(el);
			setIsAtBottom(atBottom);
			if (atBottom) {
				setAutoScroll(true);
				setHasNewMessages(false);
			} else {
				setAutoScroll(false);
			}
		},
		[computeIsAtBottom],
	);

	const handleGoToLast = useCallback(() => {
		setAutoScroll(true);
		setHasNewMessages(false);
		scrollToBottom("smooth");
	}, [scrollToBottom]);

	return (
		<AnimatePresence>
			{isOpen && (
				<motion.div
					initial={{ opacity: 0, scale: 0.95, height: 0 }}
					animate={{ opacity: 1, scale: 1, height: 500 }}
					exit={{ opacity: 0, scale: 0.95, height: 0 }}
					transition={{ duration: 0.3, ease: "easeOut" }}
					className="rounded-main fixed right-[1rem] bottom-[6rem] flex w-[375px] max-w-[90%] touch-auto flex-col justify-between overflow-hidden border border-slate-200 bg-white shadow-2xl shadow-black/10 backdrop-blur-md sm:right-[4rem] sm:bottom-[7rem]"
				>
					<ChatHeader />
					<div ref={scrollAreaRef} className="relative flex-1 overflow-y-auto" onScroll={handleScroll}>
						{!hasMessages ? (
							<div className="flex h-full flex-col items-center justify-center p-8 text-center">
								<div className="bg-primary/10 dark:bg-primary/20 border-primary/20 dark:border-primary/30 mb-6 flex h-16 w-16 items-center justify-center rounded-full border shadow-sm transition-all duration-300 hover:scale-105">
									<MessageSquare className="text-primary h-8 w-8" />
								</div>
								<h3 className="mb-3 text-xl font-semibold text-slate-800 dark:text-slate-100">Bem-vindo à Doorinha</h3>
								<p className="max-w-sm text-sm leading-relaxed text-slate-600 dark:text-slate-400">
									Faça perguntas, peça ajuda ou converse sobre qualquer assunto. Estou aqui para ajudar!
								</p>
								<div className="mt-6 flex flex-wrap justify-center gap-2">
									<span className="bg-primary/10 dark:bg-primary/20 text-primary border-primary/20 rounded-full border px-3 py-1 text-xs font-medium">
										💡 Dicas
									</span>
									<span className="bg-primary/10 dark:bg-primary/20 text-primary border-primary/20 rounded-full border px-3 py-1 text-xs font-medium">
										🤝 Ajuda
									</span>
									<span className="bg-primary/10 dark:bg-primary/20 text-primary border-primary/20 rounded-full border px-3 py-1 text-xs font-medium">
										💬 Conversa
									</span>
								</div>
							</div>
						) : (
							<div className="space-y-2 p-4">
								{messages.map(message => (
									<ChatMessage key={message.id} message={message} />
								))}
							</div>
						)}

						{/* Indicador de novas mensagens quando usuário está no histórico */}
						{hasMessages && hasNewMessages && !isAtBottom && (
							<div className="pointer-events-none absolute bottom-16 left-1/2 z-20 -translate-x-1/2 rounded-full border border-amber-300/60 bg-amber-50/90 px-3 py-1 text-xs font-medium text-amber-800 shadow-md backdrop-blur-sm select-none">
								Novas mensagens
							</div>
						)}

						{/* Botão flutuante para ir ao fim */}
						{hasMessages && !isAtBottom && (
							<button
								onClick={handleGoToLast}
								className="absolute right-3 bottom-3 z-20 inline-flex items-center gap-2 rounded-full border border-blue-300/60 bg-white/90 px-3 py-2 text-xs font-medium text-blue-700 shadow-lg backdrop-blur-md transition-all hover:scale-105 hover:bg-white focus:ring-2 focus:ring-blue-400/60 focus:outline-none"
								aria-label="Ir para a última mensagem"
							>
								<ArrowDown className="h-4 w-4" />
								<span>Ir para a última mensagem</span>
								{hasNewMessages && <span className="ml-1 inline-block h-2 w-2 rounded-full bg-red-500" />}
							</button>
						)}
					</div>
					<ChatInput
						ref={inputRef}
						onSend={sendMessage}
						onStop={stop}
						disabled={false}
						placeholder="Digite sua dúvida..."
						isStreaming={isStreaming}
					/>
				</motion.div>
			)}
		</AnimatePresence>
	);
};
