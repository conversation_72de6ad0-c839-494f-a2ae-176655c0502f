"use client";

import { toast } from "@/core/toast";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { PRODUCT_TYPE_ENDPOINTS } from "../../api/endpoints";
import { productTypeKeys } from "../../constants/query";
import { TCreateProductType } from "../../validators/create";

export const useCreateProductTypeMutation = () => {
	const queryClient = useQueryClient();

	const createProductType = useMutation({
		mutationKey: productTypeKeys.custom("create"),
		mutationFn: async (productType: TCreateProductType) => {
			const { data, success } = await createPostRequest<IMessageGlobalReturn>(PRODUCT_TYPE_ENDPOINTS.CREATE, productType);
			if (!success) throw new Error(data.message);
			return data;
		},
		onSuccess: () => productTypeKeys.invalidateAll(queryClient),
	});

	return {
		createProductType: (form: TCreateProductType) =>
			toast.promise(createProductType.mutateAsync(form), {
				loading: "Criando tipo de produto...",
				success: ({ message }) => message,
				error: ({ message }) => message,
			}),
	};
};
