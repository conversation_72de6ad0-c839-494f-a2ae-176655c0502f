export const PATHS_CONFIG = {
	DASHBOARD: {
		path: "/",
		active: true,
	},
	MEDICAO: {
		path: "/medicao",
		active: false,
		subPaths: {
			PRE_MEDICAO: {
				path: "/medicao/pre-medicao",
				active: true,
			},
			PLANILHA_MEDICAO: {
				path: "/medicao/planilha",
				active: true,
			},
		},
	},
	CICLO_PEDIDO: {
		path: "/ciclo-pedido",
		active: false,
		subPaths: {
			PEDIDOS: {
				path: "/ciclo-pedido/pedidos",
				active: true,
			},
			LOTES: {
				path: "/ciclo-pedido/lotes",
				active: true,
			},
			RESUMO: {
				path: "/ciclo-pedido/resumo",
				active: true,
			},
		},
	},
	PRODUCAO: {
		path: "/producao",
		active: false,
		subPaths: {
			CADASTROS: {
				path: "/producao/cadastros",
				active: true,
				subPaths: {
					APONTAMENTOS: {
						path: "/producao/cadastros/apontamentos",
						active: true,
					},
					ATIVIDADE: {
						path: "/producao/cadastros/atividade",
						active: true,
					},
					CELULAS: {
						path: "/producao/cadastros/celulas",
						active: true,
					},
					ESTOQUE: {
						path: "/producao/cadastros/estoque",
						active: true,
					},
					ROTEIROS: {
						path: "/producao/cadastros/roteiros",
						active: true,
					},
					SETORES: {
						path: "/producao/cadastros/setores",
						active: true,
					},
				},
			},
			ACOMPANHAMENTO: {
				path: "/producao/acompanhamento",
				active: true,
			},
			ESTATISTICAS: {
				path: "/producao/estatisticas",
				active: true,
			},
		},
	},
	INSPECTION: {
		path: "/inspecao",
		active: false,
		subPaths: {
			COLABORATORS: {
				path: "/inspecao/colaboradores",
				active: true,
			},
			INSPECTION: {
				path: "/inspecao/consulta",
				active: true,
			},
			REGISTER: {
				path: "/inspecao/cadastros",
				active: true,
			},
		},
	},
	CHAT_ADMIN: {
		path: "/chat-admin",
		active: true,
	},
} as const;

export type TPathManagerConfigKey = keyof typeof PATHS_CONFIG;
export type TPathManagerSubPathKey<T extends TPathManagerConfigKey> = (typeof PATHS_CONFIG)[T] extends { subPaths: infer S } ? keyof S : never;
