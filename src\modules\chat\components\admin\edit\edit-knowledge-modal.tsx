import { useEditKnowledgeForm } from "@/modules/chat/hooks/edit/form.hook";
import { useEditKnowledgeMutation } from "@/modules/chat/hooks/edit/mutation.hook";
import { useFindKnowledgeById } from "@/modules/chat/hooks/list/find-by-id.hook";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/shadcn/button";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/shared/components/shadcn/form";
import { Input } from "@/shared/components/shadcn/input";
import { Skeleton } from "@/shared/components/shadcn/skeleton";
import { Textarea } from "@/shared/components/shadcn/textarea";
import { Save, X } from "lucide-react";
import { useEffect } from "react";

interface EditKnowledgeModalProps {
	isOpen: boolean;
	onClose: () => void;
	id: string;
}

export const EditKnowledgeModal = ({ isOpen, onClose, id }: EditKnowledgeModalProps) => {
	const { data: fullKnowledge, isLoading } = useFindKnowledgeById(id);
	const form = useEditKnowledgeForm();
	const { updateKnowledge } = useEditKnowledgeMutation(onClose);

	useEffect(() => {
		if (isOpen && fullKnowledge) {
			form.reset({
				title: fullKnowledge.title,
				content: fullKnowledge.content ?? "",
				isActive: fullKnowledge.isActive,
			});
		}
	}, [isOpen, fullKnowledge, form]);

	const values = form.watch();

	const hasChanges =
		!!fullKnowledge &&
		(values.title !== fullKnowledge.title || values.content !== (fullKnowledge.content ?? "") || values.isActive !== fullKnowledge.isActive);

	const handleClose = () => {
		form.reset();
		onClose();
	};

	const onSubmit = (data: typeof values) => updateKnowledge(id, data);

	if (!id) return null;

	return (
		<Modal
			isOpen={isOpen}
			onClose={handleClose}
			title="Editar Conhecimento"
			description={`Editando: ${fullKnowledge?.title ?? ""}`}
			size="xl"
			className="max-h-[90vh]"
		>
			<div className="space-y-6">
				{isLoading ? (
					<div className="space-y-4">
						<Skeleton className="h-4 w-full" />
						<Skeleton className="h-32 w-full" />
						<Skeleton className="h-4 w-24" />
					</div>
				) : (
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
							<FormField
								control={form.control}
								name="title"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Título *</FormLabel>
										<FormControl>
											<Input placeholder="Digite o título do conhecimento..." {...field} className="focus:border-primary/50" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="content"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Conteúdo *</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Digite o conteúdo do conhecimento..."
												rows={8}
												{...field}
												className="focus:border-primary/50 resize-none"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="isActive"
								render={({ field }) => (
									<FormItem className="flex items-start space-x-3 border p-4">
										<FormControl>
											<Checkbox checked={field.value} onCheckedChange={field.onChange} />
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel>Conhecimento Ativo</FormLabel>
											<div className="text-muted-foreground text-sm">Conhecimento ativo será utilizado pelo chatbot</div>
										</div>
									</FormItem>
								)}
							/>
							<div className="flex justify-end gap-3">
								<Button type="button" variant="ghost" onClick={handleClose}>
									<X className="size-4" />
									Cancelar
								</Button>
								<Button type="submit" disabled={!hasChanges} className="flex items-center gap-2">
									<Save className="size-4" />
									Salvar Alterações
								</Button>
							</div>
						</form>
					</Form>
				)}
			</div>
		</Modal>
	);
};
