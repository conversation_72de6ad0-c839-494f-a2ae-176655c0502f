import { toast } from "@/core/toast";
import { useNavigatePaths } from "@/shared/hooks/utils";
import { createDeleteRequest } from "@/shared/lib/requests";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSet<PERSON>tom } from "jotai";
import { AUTH_ENDPOINTS } from "../../api/endpoints";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { userAtom } from "../../atoms/user.atom";

interface ILogoutResponse {
	success: boolean;
}

interface IUseLogoutHook {
	logout: () => Promise<void>;
}

export const useLogout = (): IUseLogoutHook => {
	const { replaceToCurrent } = useNavigatePaths();
	const queryClient = useQueryClient();
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);

	const handleLogoutSuccess = async (): Promise<void> => {
		setIsAuthenticated(false);
		setUser(null);
		queryClient.clear();
		replaceToCurrent();
	};

	const logoutMutation = useMutation<ILogoutResponse, Error>({
		mutationFn: async (): Promise<ILogoutResponse> => {
			const response = await createDeleteRequest(AUTH_ENDPOINTS.LOGOUT);
			await handleLogoutSuccess();
			return response;
		},
	});

	return {
		logout: async (): Promise<void> => {
			await toast.promise(logoutMutation.mutateAsync(), {
				loading: "Saindo...",
				error: ({ message }) => message,
			});
		},
	};
};
